apply plugin: 'com.android.application'
apply plugin: 'realm-android'
apply plugin: 'bugly'
apply plugin: 'com.huawei.agconnect'

bugly {
    appId = 'b4caf84855'
    appKey = 'fd267ac8-1735-43e1-a2c3-980f62b42d99'
}

android {
    signingConfigs {

        config {
            keyAlias 'tiku'
            keyPassword 'tiku20170608'
            storeFile file('E:\\dev0804_zhekouka\\bgquestionbank_android\\biguo.jks')
            storePassword 'biguo20170608'
            v2SigningEnabled true//v2签名
            v1SigningEnabled true//v1签名
        }
    }

    compileSdkVersion rootProject.ext.android["compileSdkVersion"]
    buildToolsVersion rootProject.ext.android["buildToolsVersion"]
    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        packagingOptions {
            exclude 'META-INF/beans.xml'
        }

        //若想新增一个productFlavors，需要在src文件夹下新建一个文件，与新的productFlavors的名称一样，右击文件夹 -> "Mark Directory as" -> Sources Root 即可
        productFlavors {
            zk {
                applicationId rootProject.ext.android["applicationId"]
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode rootProject.ext.android["versionCode"]
                versionName rootProject.ext.android["versionName"]
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        qqappid      : "tencent1106164893",
                        app_icon     : "@mipmap/ic_launcher",
                        app_name     : "@string/app_name"
                ]
            }

            ck {
                applicationId "com.dep.ckbiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.1"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

            jsz {
                applicationId "com.dep.jszbiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.0"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

            kj {
                applicationId "com.dep.kjbiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.0"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

            jzs {
                applicationId "com.dep.jzsbiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.0"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

            rlzy {
                applicationId "com.dep.rlzybiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.0"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

            yydj {
                applicationId "com.dep.yydjbiguo"
                minSdkVersion rootProject.ext.android["minSdkVersion"]
                targetSdkVersion rootProject.ext.android["targetSdkVersion"]
                versionCode 1
                versionName "1.0.0"
                signingConfig signingConfigs.config
                //极光推送配置参数
                manifestPlaceholders = [
                        JPUSH_PKGNAME: applicationId,
                        JPUSH_APPKEY : "49e1f242cc0fd29d654ded6f",
                        JPUSH_CHANNEL: "developer-default",
                        app_icon     : "@mipmap/ic_launcher"
                ]
            }

        }

        flavorDimensions "versionCode"

        ndk {
            //abiFilters 'armeabi-v7a','arm64-v8a','x86'    //armeabi-v7a：32位架构，arm64-v8a：64位架构，x86：电脑的64位架构
            abiFilters 'armeabi-v7a','arm64-v8a'          //armeabi-v7a：32位架构，arm64-v8a：64位架构
            //abiFilters 'armeabi-v7a'                      //armeabi-v7a：32位架构
            //abiFilters 'arm64-v8a'                          //arm64-v8a：64位架构
            //abiFilters 'x86'                              //x86：电脑的64位架构
        }

        manifestPlaceholders.put("APPLOG_SCHEME","rangersapplog.byAx6uYt".toLowerCase());
    }

    buildTypes {
        debug {
            buildConfigField("boolean", "DEBUG_ENABLE", "true")
            buildConfigField "boolean", "LOG_DEBUG", "true"
            buildConfigField "boolean", "USE_CANARY", "true"
            minifyEnabled false
            /*shrinkResources true
            zipAlignEnabled true*/
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.config
        }

        release {
            buildConfigField("boolean", "DEBUG_ENABLE", "false")
            buildConfigField "boolean", "LOG_DEBUG", "false"
            buildConfigField "boolean", "USE_CANARY", "false"
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        disable 'InvalidPackage'
        disable "ResourceType"
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    dexOptions {
        javaMaxHeapSize "4g"
        jumboMode = true
        preDexLibraries = false
        additionalParameters = [
                '--multi-dex',//多分包
                '--set-max-idx-number=60000'//每个包内方法数上限
        ]

    }
    /**接入保利威后，多个so来源的包合并*/
    packagingOptions {
        merge  'lib/armeabi-v7a/libc++_shared.so'
        merge  'lib/arm64-v8a/libc++_shared.so'
        merge  'lib/x86/libc++_shared.so'
        exclude  'lib/armeabi/libc++_shared.so'
        exclude  'lib/x86_64/libc++_shared.so'
        exclude  'lib/x86/libc++_shared.so'
    }
    buildFeatures {
        dataBinding true
    }


}



dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    //基础包
    implementation project(':base')
    //保利威
    implementation project(':polyvLiveCloudClassScene')
    //阿里云视，传送门：https://help.aliyun.com/zh/vod/developer-reference/test-the-demo-of-apsaravideo-player-sdk-for-android?spm=a2c4g.11186623.help-menu-29932.d_5_1_4_4_0.1a562c16xuSuJH&scm=20140722.H_383401._.OR_help-T_cn#DAS#zh-V_1
    /*implementation project (':AliyunVideoCommon')
    implementation project (':Aliyunplayer:player_demo')
    implementation project (':Aliyunplayer:AlivcPlayerTools')
    implementation project (':AliyunPlayerBase')*/
    implementation project(':zxing')
    implementation project(':Aliyunplayer:AlivcPlayerTools')
    implementation project(':Aliyunplayer:player_demo')

    //依赖注入
    annotationProcessor rootProject.ext.dependencies["dagger2-compiler"]
    annotationProcessor rootProject.ext.dependencies["butterknife-compiler"]
    //ArmsMVP架构
    implementation rootProject.ext.dependencies["retrofitUrlManager"]
    implementation rootProject.ext.dependencies["arms"]
    implementation rootProject.ext.dependencies["arms-glide"]
    //缓存
    implementation rootProject.ext.dependencies["mmkv"]
    //万能适配器
    implementation rootProject.ext.dependencies["baseadapter"]
    //RecyclerView多种类型的item
    implementation rootProject.ext.dependencies["multitype"]
    //banner控件
    implementation rootProject.ext.dependencies["banner"]
    //初始化加载框架
    implementation rootProject.ext.dependencies["loadsir"]

    //支付宝所需
    implementation "com.alipay.sdk:alipaysdk-android:15.8.17@aar"
    //implementation(name: 'alipaysdk-noutdid-15.8.03.210428205839', ext: 'aar')

    //巨量引擎
    // Applog 上报组件（必须），传送门：https://open.oceanengine.com/labels/7/docs/1696710651039744
    implementation 'com.bytedance.applog:RangersAppLog-Lite-cn:6.16.9'
    // 商业化转化组件（必须）
    implementation 'com.bytedance.ads:AppConvert:*******'
    //巨量检查工具
    //debugImplementation 'com.bytedance.applog:RangersAppLog-DevTools:3.4.8'

    //腾讯Bugly
    //implementation 'com.tencent.bugly:crashreport_upgrade:latest.release'

    //极光一键登录
    implementation 'cn.jiguang.sdk:jverification:3.3.1'
    //极光推送
    implementation 'cn.jiguang.sdk:jpush:5.5.3'

    //QQ登录
    //implementation 'com.tencent.tauth:qqopensdk:3.52.0'

    //友盟基础库相关依赖（必须）
    implementation 'com.umeng.umsdk:common:9.7.9'
    implementation 'com.umeng.umsdk:asms:1.8.4'
    //友盟Deeplink
    implementation(name: 'umeng-link-2.0.0', ext: 'aar')
    //友盟分享
    //友盟Push相关依赖（必须）
    implementation 'com.umeng.umsdk:push:6.7.0'
    implementation 'com.umeng.umsdk:share-core:7.3.5'//分享核心库，必选
    implementation 'com.umeng.umsdk:share-board:7.3.5'//分享面板功能，可选
    implementation 'com.umeng.umsdk:share-wx:7.3.5'//微信完整版
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.24'//微信官方依赖库，必选
    implementation 'com.umeng.umsdk:share-qq:7.3.5'//QQ完整版
    implementation files('libs/open_sdk_3.5.16.4_r8c01346_lite.jar')//QQ官方依赖库，必选
    //华为推送厂商通道
    implementation 'com.umeng.umsdk:huawei-umengaccs:2.1.0'
    implementation 'com.huawei.hms:push:6.12.0.300'
    //小米厂商推送通道
    implementation 'com.umeng.umsdk:xiaomi-umengaccs:2.1.0'
    implementation 'com.umeng.umsdk:xiaomi-push:5.9.9'
    //OPPO厂商推送通道
    implementation 'com.umeng.umsdk:oppo-umengaccs:2.1.0'
    implementation 'com.umeng.umsdk:oppo-push:3.4.0'
    //VIVO厂商推送通道
    implementation 'com.umeng.umsdk:vivo-umengaccs:2.1.0'
    implementation 'com.umeng.umsdk:vivo-push:3.0.0.7'

    //高德地图定位
    //implementation files('libs/AMap_Location_V6.3.0_20230410.jar')
    //implementation 'com.amap.api:location:latest.integration'
    //高德3D地图
    //implementation 'com.amap.api:3dmap:latest.integration'
    //级联菜单
    implementation 'com.contrarywind:Android-PickerView:3.2.7'
    //滚轮，传送门：https://github.com/weidongjian/androidWheelView
    implementation 'com.github.weidongjian:androidWheelView:1.0.0'

    //修复CoordinatorLayout布局中，滑动AppBarLayout时又滑动RecyclerView引起的颤动
    implementation 'com.github.yuruiyin:AppbarLayoutBehavior:v1.0.2'

    //实现RecyclerView瀑布流需要的依赖包
    implementation 'com.google.android:flexbox:2.0.1'

    //RecyclerView悬浮粘性头部
    implementation 'com.github.qdxxxx:StickyHeaderDecoration:1.0.1'

    //刷新、加载更多，传送门：https://github.com/scwang90/SmartRefreshLayout
    //解决官方的SwipeRefreshLayout控件嵌套CoordinatorLayout控件的联动问题
    implementation  'io.github.scwang90:refresh-layout-kernel:2.0.5'      //核心必须依赖
    implementation  'io.github.scwang90:refresh-header-material:2.0.5'    //经典刷新头
    implementation  'io.github.scwang90:refresh-footer-classics:2.0.5'    //经典加载

    //优量汇广告
    //implementation 'com.qq.e.union:union:4.580.1450'
    //穿山甲广告
    implementation 'com.pangle.cn:ads-sdk-pro:*******'

    //预览PDF
    implementation 'es.voghdev.pdfviewpager:library:1.1.2'
    //下载文件
    implementation 'com.github.liqinew:nohttprxutils:v.2.0.7'

    //文字上下滚动
    //implementation 'com.github.Brioal:ADTextView:1.2'
    //implementation 'com.github.maning0303:SwitcherView:v1.0.7'
    //implementation 'com.lester:scolltextview:1.0.0'
    implementation 'com.github.pirrip90:ScrollTextView:1.0.0'

    //数据抓包工具
    implementation 'com.readystatesoftware.chuck:library:1.1.0'
    //releaseImplementation 'com.readystatesoftware.chuck:library-no-op:1.1.0'

    //虚线
    implementation 'com.github.SenhLinsh:LshUtils:1.2.0'

    //带有文字的Switch开关，圈子的最热和最新切换开关
    implementation 'com.ld:switchView:1.1.7'
    //与PageView联动滚动的TabLayout，传送门：https://github.com/junixapp/FlycoTabLayout/blob/master/README_CN.md
    implementation 'com.github.li-xiaojun:FlycoTabLayout:2.0.8'
}
