package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import android.view.View;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.BiguoVipOpenBean;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.BiguoVipOpenActivityBinding;
import com.dep.biguo.di.component.DaggerBiguoVipOpenComponent;
import com.dep.biguo.dialog.RuleDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.BiguoVipOpenContract;
import com.dep.biguo.mvp.presenter.BiguoVipOpenPresenter;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.adapter.BiguoVipOpenAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class BiguoVipOpenActivity extends BaseLoadSirActivity<BiguoVipOpenPresenter> implements BiguoVipOpenContract.View,View.OnClickListener {
    private BiguoVipOpenActivityBinding binding;
    private NormalToolbarUtil toolbarUtil;

    private BiguoVipOpenAdapter yesAdapter;
    private BiguoVipOpenAdapter noAdapter;

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerBiguoVipOpenComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_open_activity);
        binding.setOnClickListener(this);
        toolbarUtil = new NormalToolbarUtil(this)
                .setLeftDrawableRes(R.drawable.arrow_white_back)
                .setMustDay(this, true)
                .setAnchorView(binding.ruleView)
                .setCenterText("笔果专享折扣卡")
                .setCenterTextColor(Color.WHITE);
        toolbarUtil.setFollowScrollListener(binding.nestedScrollView, (toolbar, changeRate, isDay) -> {
                    //计算图标的颜色，深色模式,图标只需要白色
                    int iconRgb = Math.max(102, 255 - (int) ((changeRate) * 255));
                    int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

                    //计算文字的颜色，深色模式,文字只需要白色 
                    int textRgb = Math.max(51, 255 - (int) ((changeRate) * 255));
                    int textColor = Color.argb(255, textRgb, textRgb, textRgb);

                    setIconAndTextAndBackgroundColor(iconColor, textColor);
                });

        yesAdapter = new BiguoVipOpenAdapter(true);
        binding.yesRecyclerView.setAdapter(yesAdapter);

        noAdapter = new BiguoVipOpenAdapter(false);
        binding.noRecyclerView.setAdapter(noAdapter);

        return 0;
    }

    /**给标题栏的图标和文字着色
     * @param iconColor       图标颜色
     * @param rightTextColor  标题文字颜色
     */
    public void setIconAndTextAndBackgroundColor(int iconColor, int rightTextColor){
        toolbarUtil.setIconAndTextColor(iconColor, rightTextColor, Color.WHITE);
    }

    @Override
    public View initLoadSir() {
        return binding.nestedScrollView;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onRequest() {
        mPresenter.getData(true);
    }

    public void onClick(View view){
        if(view == binding.openVipView){
            if(!MainAppUtils.checkLogin(BiguoVipOpenActivity.this)) return;
            BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
            String content = String.format(Locale.CHINA, "笔果折扣卡开通后可享%.1f折优惠，题库仍需单独购买!", Float.parseFloat(bean.getDiscount()) * 10);

            new MessageDialog.Builder(getSupportFragmentManager())
                    .setTitle("温馨提示")
                    .setContent(content)
                    .setNegativeText("关闭")
                    .setPositiveText("我要购买")
                    .setPositiveClickListener(v -> mPresenter.getDiscountCard(0))
                    .builder()
                    .show();

        }else if(view == binding.openGroupVipLayout){
            if(!MainAppUtils.checkLogin(BiguoVipOpenActivity.this)) return;
            BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
            String content = String.format(Locale.CHINA, "邀请1名新用户参团享半价开通折扣卡，开通后可享%.1f折优惠;24小时内未成团自动退款!", Float.parseFloat(bean.getDiscount()) * 10);

            if(mPresenter.getBiguoVipBean().getGroup_info() == null){
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent(content)
                        .setNegativeText("关闭")
                        .setPositiveText("我要购买")
                        .setPositiveClickListener(v -> mPresenter.getDiscountCard(1))
                        .builder()
                        .show();
            }else {
                BiguoVipOpenBean.GroupInfo groupInfo = bean.getGroup_info();
                String shareUrl = groupInfo.getShare_url();
                if(!shareUrl.contains("?")){
                    shareUrl += "?";
                }
                shareUrl = shareUrl
                        + (shareUrl.endsWith("?") ? "" : "&")
                        + "group_id=" + groupInfo.getGroup_id()
                        + "&users_id=" + groupInfo.getUsers_id()
                        + "&inviter_id=" + groupInfo.getUsers_id();
                new ShareDialog.Builder(getActivity())
                        .setShareTitle("您有一个好友拼团福利待领取")
                        .setShareContent("新用户注册并加入拼团，立享半价解锁笔果折扣卡！")
                        .setShareUrl(shareUrl)
                        .setOnShareListener((type) -> showMessage("分享成功，请等待被邀请用户注册并参与拼团"))
                        .builder()
                        .show();
            }

        }else if(view == binding.serviceAgreementView){
            HtmlActivity.start(this, Constant.AGREEMENT_USER3);

        }else if(view == binding.ruleView){
            mPresenter.getRule("membership_rule");
        }
    }

    @Override
    public void setDataSuccess(BiguoVipOpenBean bean) {
        //如果列表是空的，则创建一个数组
        if(AppUtil.isEmpty(bean.getEnjoy_discounts())){
            bean.setEnjoy_discounts(new ArrayList<>());
        }
        if(AppUtil.isEmpty(bean.getNot_enjoy_discounts())){
            bean.setNot_enjoy_discounts(new ArrayList<>());
        }

        binding.saveMoneyView.setText(String.format("%s", bean.getHighest_reduce_price()));
        binding.priceAndValidityView.setText(String.format("%s元开通%s年", bean.getPrice(), bean.getOpen_effective_year()));
        try {
            binding.discountView.setText(String.format(Locale.CHINA,"专享%.1f折扣价", Float.parseFloat(bean.getDiscount()) * 10));
        }catch (Exception e){
            binding.discountView.setText("专享折扣价");
        }
        binding.enjoyCountView.setText(String.format("享%s项", getDiscountsCount(bean.getEnjoy_discounts())));
        if(bean.getGroup_info() == null){
            binding.openGroupVipView.setText(String.format("¥%s 立即邀请\n邀请1名新用户参团", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.countDownTimeView.setVisibility(View.GONE);
        }else {
            binding.openGroupVipView.setText(String.format("¥%s 拼团中", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.countDownTimeView.setVisibility(View.VISIBLE);
        }
        binding.openVipView.setText(String.format("¥%s  立即开通", bean.getPrice()));
        binding.openCountView.setText(String.format("已有%s人开通", bean.getTotal_people_num()));

        //填充空白数据，占据位置，使得不享受列表和享受列表的高度一致
        for (int i=0;i<Math.max(bean.getEnjoy_discounts().size(), bean.getNot_enjoy_discounts().size()); i++){
            if(i >= bean.getEnjoy_discounts().size()) {
                bean.getEnjoy_discounts().add(new BiguoVipOpenBean.DiscountsBean());
            }
            if(i >= bean.getNot_enjoy_discounts().size()) {
                bean.getNot_enjoy_discounts().add(new BiguoVipOpenBean.DiscountsBean());
            }
        }
        yesAdapter.setNewData(bean.getEnjoy_discounts());
        noAdapter.setNewData(bean.getNot_enjoy_discounts());

    }

    public int getDiscountsCount(List<BiguoVipOpenBean.DiscountsBean> list){
        int count = 0;
        for (int i=0;i<list.size(); i++){
            if(!AppUtil.isEmpty(list.get(i).getTitle())) {
                count ++;
            }
        }
        return count;
    }

    @Override
    public void refreshCountTime(long countTime) {
        binding.countDownTimeView.setText(TimeFormatUtils.formatMillisecond(countTime));
    }

    @Override
    public void showPayDialog(int isGroup, List<DiscountBean> allDiscount) {
        BiguoVipOpenBean openBean = mPresenter.getBiguoVipBean();
        String payPrice = isGroup == StartFinal.YES ? openBean.getGroup_price() : openBean.getPrice();
        new DiscountPayDialog.Builder(this, PayUtils.MEMBERSHIP)
                .setGoodsName("笔果折扣卡")
                .setShowGuobi(false)
                .setPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setPayPrice(Float.parseFloat(AppUtil.isEmpty(payPrice, "9999")))
                .setDiscountList(allDiscount)
                .setOnPayListener((joinGroup, discount, payType) -> {
                    int coupon_id = discount == null ? 0 : discount.getId();
                    mPresenter.payOrder(payType, 0, coupon_id, isGroup, openBean.getGroup_id());
                })
                .build()
                .show();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Override
    public void paySuccess() {
        showMessage("支付成功");
        UserBean userBean = UserCache.getUserCache();
        userBean.setMembership(1);
        UserCache.cacheUser(userBean);
        ArmsUtils.startActivity(BiguoVipActivity.class);
        finish();
    }

    @Override
    public void getRuleSuccess(List<String> rule) {
        new RuleDialog(this)
                .setTitleText("购买须知")
                .setRules(rule)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mPresenter !=null && mPresenter.getBiguoVipBean() != null) {
            mPresenter.getData(false);
        }
    }

    public void setToolBarErrorStyle(boolean isSuccess){
        if(isSuccess){
            setIconAndTextAndBackgroundColor(Color.WHITE, Color.WHITE);
        }else {
            setIconAndTextAndBackgroundColor(AppUtil.getColorRes(this, R.color.tblack), AppUtil.getColorRes(this, R.color.tblack));
        }
    }

    @Override
    public void showEmptyView() {
        super.showEmptyView();
        setToolBarErrorStyle(false);
    }

    @Override
    public void showErrorView(Throwable e) {
        super.showErrorView(e);
        setToolBarErrorStyle(false);
    }

    @Override
    public void showSuccessView() {
        super.showSuccessView();
        setToolBarErrorStyle(true);
    }
}