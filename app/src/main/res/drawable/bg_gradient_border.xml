<!-- res/drawable/bg_gradient_border.xml -->
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <!-- 定义一个线性渐变 -->
    <gradient
        android:type="linear"
        android:angle="0"
        android:startColor="#F06292"
        android:endColor="#FFB74D" />

    <!--
      圆角半径必须与CardView的半径一致或更大，
      这里我们设置为与您的卡片相同的 12dp。
    -->
    <corners android:radius="12dp" />

</shape>