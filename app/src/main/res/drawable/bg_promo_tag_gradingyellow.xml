<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:width="170dp" android:height="160dp">
        <shape android:shape="rectangle">
            <stroke android:width="1dp" android:color="#ffffe0a0" />
            <gradient android:type="linear" android:useLevel="true" android:startColor="#ffffe2b2" android:centerColor="#fffffefc" android:endColor="#ffffe0a0" android:angle="315" />
            <corners android:radius="10dp" />
            <item name="android:shadowColor">#80ffffff</item>
            <item name="android:shadowDx">0</item>
            <item name="android:shadowDy">1</item>
        </shape>
    </item>
</selector>